// ==============================
// 文件：BuildAssetBundlesEditor.cs（通用）
// 作用：一键构建 AssetBundles（LZ4），并生成 manifest.json（VersionManifest 格式）
// 使用：菜单 Assets/CF Build/Build AssetBundles
// 说明：默认输出到 Assets/AssetBundles/{Platform}/
// ==============================
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using CommonFramework.Utils;

public static class BuildAssetBundlesEditor
{
    [MenuItem("Assets/CF Build/Build AssetBundles")]
    public static void BuildAll()
    {
        // 1) 输出路径
        string root = Path.Combine(Application.dataPath, "../AssetBundles");
        string platform = GetPlatformFolderName();
        string outDir = Path.Combine(root, platform);
        Directory.CreateDirectory(outDir);

        // 2) 构建（LZ4：ChunkBasedCompression）
        var buildOpts = BuildAssetBundleOptions.ChunkBasedCompression | BuildAssetBundleOptions.StrictMode;
        var target = EditorUserBuildSettings.activeBuildTarget;
        var main = BuildPipeline.BuildAssetBundles(outDir, buildOpts, target);
        if (main == null)
        {
            Debug.LogError("[CF] BuildAssetBundles failed.");
            return;
        }

        // 3) 生成 manifest.json（读取目录下所有 bundle 文件）
        var vm = new VersionManifest { version = (int)System.DateTimeOffset.Now.ToUnixTimeSeconds(), bundles = new List<BundleInfo>() };
        foreach (var file in Directory.GetFiles(outDir))
        {
            var name = Path.GetFileName(file);
            if (name.EndsWith(".manifest") || name.Equals(platform)) continue; // 跳过 .manifest 与 主清单
            var info = new FileInfo(file);
            vm.bundles.Add(new BundleInfo
            {
                name = name,
                size = info.Length,
                hash = MD5Of(file),
                deps = main.GetAllDependencies(name)
            });
        }

        var json = JsonUtility.ToJson(vm, true);
        File.WriteAllText(Path.Combine(outDir, "manifest.json"), json, Encoding.UTF8);

        AssetDatabase.Refresh();
        Debug.Log($"[CF] AssetBundles Built -> {outDir}\nmanifest.json generated.");
    }

    private static string GetPlatformFolderName()
    {
#if UNITY_STANDALONE_WIN
        return "StandaloneWindows64";
#elif UNITY_ANDROID
        return "Android";
#elif UNITY_IOS
        return "iOS";
#else
        return EditorUserBuildSettings.activeBuildTarget.ToString();
#endif
    }

    private static string MD5Of(string file)
    {
        using var md5 = MD5.Create();
        using var stream = File.OpenRead(file);
        var hash = md5.ComputeHash(stream);
        var sb = new StringBuilder();
        foreach (var b in hash) sb.Append(b.ToString("x2"));
        return sb.ToString();
    }
}
