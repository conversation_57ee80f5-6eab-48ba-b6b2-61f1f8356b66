-- pipes_move.lua
-- 功能：将物体以固定速度向左移动，超出屏幕左侧一定偏移后销毁（用于 Flappy Bird 管道等）

-- =========================
-- == 可在 Inspector 注入 ==
-- =========================
-- 1) 速度（数值）：Name = n:speed,         Use Object = ✗, Number = 5（示例）
-- 2) 左侧偏移（数值）：Name = n:leftInset,   Use Object = ✗, Number = 1（屏幕外再多走 1 个单位才销毁）
-- 3) 相机（对象）：   Name = camera,        Use Object = ✓, Obj = 场景中的 Camera（可选，不填用 Camera.main）
-- 4) 销毁目标（对象）：Name = destroyTarget, Use Object = ✓, Obj = 要一并销毁的根物体（可选，不填就销毁 self）

-- #region ========= C# 类型缓存 =========
local CS        = CS                                                     -- xLua 的 C# 入口
local Vector3   = CS.UnityEngine.Vector3                                 -- 三维向量（用于位移与屏幕点）
local Time      = CS.UnityEngine.Time                                    -- 时间（deltaTime）
local Camera    = CS.UnityEngine.Camera                                  -- 相机类型（取 main）
local Object    = CS.UnityEngine.Object                                  -- 用于销毁
local Debug     = CS.UnityEngine.Debug                                   -- 日志输出
-- #endregion

-- #region ========= 配置与状态 =========
local M = {
    speed      = 5.0,        -- 运动速度（单位/秒）——可注入 n:speed
    leftInset  = 1.0,        -- 销毁时的左侧额外偏移（越过屏幕左缘再减去这个值）——可注入 n:leftInset
    _leftEdge  = -99999.0,   -- 运行时计算出的屏幕左界（世界坐标）
    _camera    = nil,        -- 使用中的相机（可注入 camera；否则用 Camera.main）
    _killGO    = nil,        -- 需要销毁的根对象（可注入 destroyTarget；否则销毁自身）
}
-- #endregion

-- #region ========= 生命周期：Start =========
function M:Start()
    -- —— 读取 Inspector 注入的数值（LuaBehaviour 会把 n:xxx 写入为同名变量）——
    if speed     ~= nil then self.speed     = speed     end              -- 若注入了 n:speed，覆盖默认
    if leftInset ~= nil then self.leftInset = leftInset end              -- 若注入了 n:leftInset，覆盖默认

    -- —— 读取可注入对象：camera / destroyTarget —— 
    if camera ~= nil then self._camera = camera end                      -- 注入相机（可为空）
    if destroyTarget ~= nil then                                          -- 注入销毁目标（可为空）
        -- 允许拖组件或 GameObject：统一取到 GameObject
        self._killGO = destroyTarget.gameObject and destroyTarget.gameObject or destroyTarget
    end

    -- —— 相机兜底：未注入则使用 Camera.main —— 
    if self._camera == nil then
        self._camera = Camera.main                                        -- 可能为 nil（无主相机时）
        if self._camera == nil then
            Debug.LogWarning("[Lua][Pipes] 未找到 Camera（无注入且无 Camera.main），越界计算将不生效。")
            return                                                        -- 无相机无法计算左界，提前返回
        end
    end

    -- —— 计算屏幕左界（世界坐标）：屏幕(0,0) 转世界，再向左偏移 leftInset —— 
    --     注：ScreenToWorldPoint 需要一个 3D 向量，Z 取相机到物体的距离；对 2D 正交相机可用 (0,0,0)
    local leftWorld = self._camera:ScreenToWorldPoint(Vector3(0, 0, 0))  -- 屏幕左下角 → 世界坐标
    self._leftEdge  = leftWorld.x - self.leftInset                        -- 再向左退一点点，避免刚出屏幕就销毁
end
-- #endregion

-- #region ========= 生命周期：Update =========
function M:Update()
    -- —— 1) 按速度向左移动：x' = x + (-1,0,0) * speed * dt —— 
    self.transform.position = self.transform.position + Vector3.left * self.speed * Time.deltaTime

    -- —— 2) 若没有有效左界（例如没有相机），直接返回，不做销毁判定 —— 
    if self._camera == nil then return end

    -- —— 3) 越界判定并销毁（优先销毁注入的根对象，否则销毁自身） —— 
    if self.transform.position.x < self._leftEdge then
        local target = self._killGO or self.gameObject                   -- 目标为空则用自身
        Object.Destroy(target)                                           -- 直接销毁（若有对象池请改为回收）
    end
end
-- #endregion

return M
