local util = require 'xlua.util'           -- xlua 常用工具

-- 绑定/缓存常用的 Unity 类型（跨语言调用少一次查找，省开销）
local UnityEngine = CS.UnityEngine
local Vector3      = CS.UnityEngine.Vector3
local Vector2      = CS.UnityEngine.Vector2
local Time         = CS.UnityEngine.Time
local DOTween      = CS.DG.Tweening.DOTween   -- 这里目前没用到，可以删掉

-- 可调参数
local speed_pipes = 5   -- 管子向左移动速度（单位：世界坐标/秒）
local leftEdge = nil     -- 记录屏幕左侧世界坐标，用于判定“出屏”

-- Mono 生命周期：Awake（此脚本挂在的对象实例化后最早回调）
function Awake()
    print("lua Awake...")
    -- 这里现在什么也没做；通常可在此做只需做一次的初始化
end

-- Start：Awake 之后，第一帧前调用
function Start()
    print("lua Start...")
    -- Camera.main:ScreenToWorldPoint(Vector3.zero).x 取得屏幕左下角的世界坐标 X
    -- 为避免边界刚好贴边导致抖动，向左再偏移 1 个单位
    leftEdge = UnityEngine.Camera.main:ScreenToWorldPoint(Vector3.zero).x - 1
end

-- Update：每帧调用
function Update()
    -- 保护：若 prefab 没被正确赋值，提前返回避免空引用
    if not prefab or not prefab.transform then return end

    -- 每帧按速度向左移动（帧率无关：乘以 deltaTime）
    prefab.transform.position =
        prefab.transform.position + Vector3.left * speed_pipes * Time.deltaTime

    -- 超出左边界：回收/销毁
    if prefab.transform.position.x < leftEdge then
        -- ⚠ 下面这句只是把局部变量设为 nil，不会销毁场景中的对象！
        -- GameObject = nil

        -- 正确做法二选一：
        -- 1) 直接销毁
        UnityEngine.Object.Destroy(prefab)

        -- 2) 或者做对象池：设为不激活，等待复用
        -- prefab:SetActive(false)
    end
end
