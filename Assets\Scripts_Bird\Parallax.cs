﻿using UnityEngine; // 引入 Unity 引擎 API（MonoBehaviour、MeshRenderer、Vector2 等）

public class Parallax : MonoBehaviour
{
    // 当前物体的 MeshRenderer 组件引用（用于访问材质和贴图）
    private MeshRenderer meshRenderer;

    // 背景滚动速度（单位：纹理 UV 坐标的偏移量/秒）
    public float animationSpeed = 1f;

    // Awake 在脚本加载时调用（比 Start 更早）
    private void Awake()
    {
        // 获取当前物体上的 MeshRenderer 组件
        meshRenderer = GetComponent<MeshRenderer>();
    }

    // Update 每帧调用一次
    private void Update()
    {
        // 让材质的主纹理（mainTexture）的 UV 偏移量在 X 方向不断增加
        // 形成背景水平移动的视觉效果
        // Time.deltaTime 确保在不同帧率下速度一致
        meshRenderer.material.mainTextureOffset += new Vector2(animationSpeed * Time.deltaTime, 0);
    }
}
