// ==============================
// 文件：ABNameAssigner.cs（通用）
// 作用：批量根据文件夹设置 AssetBundle 名 或 清除
// 使用：选中一个或多个文件夹，菜单 Assets/CF Build/Assign AB Names / Clear AB Names
// 规则：bundleName = 选中文件夹名（小写）；变体留空
// ==============================
using System.IO;
using UnityEditor;
using UnityEngine;

public static class ABNameAssigner
{
    [MenuItem("Assets/CF Build/Assign AB Names")]
    public static void Assign()
    {
        var objs = Selection.GetFiltered(typeof(DefaultAsset), SelectionMode.Assets);
        foreach (var obj in objs)
        {
            var path = AssetDatabase.GetAssetPath(obj);
            if (!AssetDatabase.IsValidFolder(path)) continue;

            string bundleName = new DirectoryInfo(path).Name.ToLowerInvariant();

            foreach (var file in Directory.GetFiles(path, "*", SearchOption.AllDirectories))
            {
                if (file.EndsWith(".meta")) continue;
                var assetPath = file.Replace(Application.dataPath, "Assets").Replace("\\", "/");
                var importer = AssetImporter.GetAtPath(assetPath);
                if (importer != null)
                {
                    importer.SetAssetBundleNameAndVariant(bundleName, "");
                }
            }
        }
        AssetDatabase.SaveAssets();
        Debug.Log("[CF] AB names assigned.");
    }

    [MenuItem("Assets/CF Build/Clear AB Names")]
    public static void ClearABNames()
    {
        var all = AssetDatabase.GetAllAssetBundleNames();
        foreach (var n in all) AssetDatabase.RemoveAssetBundleName(n, true);
        Debug.Log("[CF] AB names cleared.");
    }
}
