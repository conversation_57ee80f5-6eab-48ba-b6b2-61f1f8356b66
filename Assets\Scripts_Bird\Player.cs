﻿using UnityEngine;

// 强制要求挂在同一物体上的 SpriteRenderer（如果没有会自动添加）
// 这样可以确保 GetComponent<SpriteRenderer>() 一定有结果，避免空引用。
[RequireComponent(typeof(SpriteRenderer))]
public class Player : MonoBehaviour
{
    // ========== 配置字段（可在 Inspector 中修改） ==========

    // 小鸟的翅膀帧数组（将逐帧切换 Sprite 实现简单动画）
    [SerializeField] private Sprite[] sprites;

    // 上一次点击给予的向上初速度（单位：世界坐标单位/秒）
    [SerializeField] private float strength = 5f;

    // 自定义“重力”加速度（通常为负数，向下）
    [SerializeField] private float gravity = -9.81f;

    // 倾斜系数：速度乘以该系数等于目标角度（用于视觉上小鸟前倾/仰角）
    [SerializeField] private float tilt = 5f;

    // 最大倾斜角度（避免旋转超出视觉范围）
    [SerializeField] private float maxTilt = 45f;

    // 动画帧切换间隔（秒）
    [SerializeField] private float spriteFrameInterval = 0.15f;

    // ========== 私有状态（运行时缓存） ==========

    // 精灵渲染组件的缓存引用（用于设置 sprite）
    private SpriteRenderer spriteRenderer;

    // 当前动画帧索引
    private int spriteIndex = 0;

    // 当前的速度向量（只使用 y 分量控制上下）
    private Vector3 direction = Vector3.zero;

    // 动画协程引用（便于启停）
    private Coroutine animCoroutine;

    // 缓存的 GameManager 引用（避免每次碰撞都调用 Find）
    private GameManager cachedGameManager;

    // ========== MonoBehaviour 生命周期方法 ==========

    // Awake 在脚本启用（或物体激活）时最先调用：做轻量级缓存与初始化
    private void Awake()
    {
        // 缓存 SpriteRenderer（RequireComponent 确保存在）
        spriteRenderer = GetComponent<SpriteRenderer>();

        // 尝试缓存 GameManager（场景中通常只有一个）
        cachedGameManager = FindObjectOfType<GameManager>();

        // 保证 sprites 数组非空，否则在运行时会报错
        if (sprites == null || sprites.Length == 0)
        {
            Debug.LogWarning("[Player] sprites 数组为空，动画将不会播放，请在 Inspector 中赋值。");
        }
    }

    // Start 在第一次 Update 之前调用：启动动画协程
    private void Start()
    {
        // 如果 sprites 有内容，则启动逐帧动画协程
        if (sprites != null && sprites.Length > 0)
        {
            // 先确保没有残留协程
            if (animCoroutine != null) StopCoroutine(animCoroutine);
            animCoroutine = StartCoroutine(AnimateSpriteCoroutine());
        }
    }

    // OnEnable 在物体/脚本启用时调用：用于重置状态（每次开始游戏都会调用）
    private void OnEnable()
    {
        // 将小鸟的竖直坐标重置为 0（保持 x 不变）
        Vector3 pos = transform.position;
        pos.y = 0f;
        transform.position = pos;

        // 重置当前速度，避免上一次游戏残留影响
        direction = Vector3.zero;

        // 确保动画协程在启用时运行（防止某些情况下未启动）
        if (sprites != null && sprites.Length > 0 && animCoroutine == null)
        {
            animCoroutine = StartCoroutine(AnimateSpriteCoroutine());
        }
    }

    // OnDisable 在物体/脚本禁用时调用：停止协程，清理资源
    private void OnDisable()
    {
        // 停止动画协程（避免协程在对象禁用后仍在运行）
        if (animCoroutine != null)
        {
            StopCoroutine(animCoroutine);
            animCoroutine = null;
        }
    }

    // 每帧调用（游戏逻辑主循环）: 处理输入、速度更新、位置更新与旋转
    private void Update()
    {
        // ========== 输入：支持键盘、鼠标、触摸 ==========
        // 空格键按下或鼠标左键按下视为“拍打”
        if (Input.GetKeyDown(KeyCode.Space) || Input.GetMouseButtonDown(0))
        {
            // 点击时给向上初速度（覆盖当前 y 速度）
            direction = Vector3.up * strength;
        }
        else
        {
            // 移动设备上的触摸支持：只处理触摸开始阶段
            if (Input.touchCount > 0)
            {
                Touch t = Input.GetTouch(0);
                if (t.phase == TouchPhase.Began)
                {
                    direction = Vector3.up * strength;
                }
            }
        }

        // ========== 物理更新（手动模拟重力） ==========
        // 将重力乘以 deltaTime 累加到 y 速度（Time.deltaTime 保证帧率无关）
        direction.y += gravity * Time.deltaTime;

        // 用当前速度更新位移（直接修改 transform）
        transform.position += direction * Time.deltaTime;

        // ========== 根据竖直速度调整旋转角度（视觉倾斜） ==========
        // 计算目标角度（竖直速度 * 倾斜系数），并限制在 [-maxTilt, maxTilt] 范围内
        float zAngle = Mathf.Clamp(direction.y * tilt, -maxTilt, maxTilt);

        // 应用旋转（只修改 Z 轴，保持 X/Y 欧拉角不变）
        Vector3 euler = transform.eulerAngles;
        euler.z = zAngle;
        transform.eulerAngles = euler;
    }

    // ========== 动画协程：逐帧切换小鸟贴图 ==========
    private System.Collections.IEnumerator AnimateSpriteCoroutine()
    {
        // 如果没有 sprites 或长度为 0，直接退出协程
        if (sprites == null || sprites.Length == 0)
            yield break;

        // 无限循环：每隔 spriteFrameInterval 切换一帧
        while (true)
        {
            // 切换到下一帧索引
            spriteIndex++;
            if (spriteIndex >= sprites.Length) spriteIndex = 0; // 循环播放

            // 额外安全检查：索引有效时设置 sprite
            if (spriteIndex >= 0 && spriteIndex < sprites.Length)
            {
                spriteRenderer.sprite = sprites[spriteIndex];
            }

            // 等待指定时间再切换下一帧（不使用 WaitForSecondsRealtime，依赖 Time.timeScale）
            yield return new WaitForSeconds(spriteFrameInterval);
        }
    }

    // ========== 碰撞检测：触发器用来判定碰到障碍或计分区 ==========
    private void OnTriggerEnter2D(Collider2D other)
    {
        // 防御式编程：如果 cachedGameManager 为 null，尝试再次查找并缓存
        if (cachedGameManager == null)
        {
            cachedGameManager = FindObjectOfType<GameManager>();
        }

        // 如果碰到障碍（标签为 Obstacle），通知 GameManager 游戏结束
        if (other.gameObject.CompareTag("Obstacle"))
        {
            // 如果找不到 GameManager，打印警告并直接禁用玩家（保守处理）
            if (cachedGameManager == null)
            {
                Debug.LogWarning("[Player] 未找到 GameManager（GameOver 无法通知）。");
                // 如果无法通知 GameManager，也最好关闭玩家控制以避免继续运动
                enabled = false;
                return;
            }

            // 调用 GameManager 的 GameOver 方法（由 GameManager 处理 UI/暂停等）
            cachedGameManager.GameOver();
        }
        // 如果碰到计分区（标签为 Scoring），通知增加分数
        else if (other.gameObject.CompareTag("Scoring"))
        {
            // 再次防御：若没有 GameManager，尝试查找
            if (cachedGameManager == null)
            {
                cachedGameManager = FindObjectOfType<GameManager>();
                if (cachedGameManager == null)
                {
                    Debug.LogWarning("[Player] 未找到 GameManager（IncreaseScore 无法通知）。");
                    return;
                }
            }

            // 调用 GameManager 的加分方法
            cachedGameManager.IncreaseScore();
        }
    }
}
