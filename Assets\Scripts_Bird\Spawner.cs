﻿using UnityEngine; // 引入 Unity 引擎命名空间，提供游戏对象、组件、向量等 API

public class Spawner : MonoBehaviour
{
    // 生成的预制体（Prefab），这里是管道组对象
    public GameObject prefab;

    // 生成间隔（秒），越小生成越快
    public float spawnRate = 1f;

    // 管道生成的最小垂直偏移量
    public float minHeight = -1f;

    // 管道生成的最大垂直偏移量
    public float maxHeight = 2f;

    // 当该脚本所在的对象启用时调用
    private void OnEnable()
    {
        // 重复调用 Spawn 方法
        // 参数1：方法名
        // 参数2：首次延迟时间（spawnRate秒后第一次生成）
        // 参数3：之后的调用间隔（每隔 spawnRate 秒生成一次）
        InvokeRepeating(nameof(Spawn), spawnRate, spawnRate);
    }

    // 当该脚本所在的对象禁用时调用
    private void OnDisable()
    {
        // 停止调用 Spawn 方法
        CancelInvoke(nameof(Spawn));
    }

    // 实际生成管道的方法
    private void Spawn()
    {
        // 在当前 Spawner 位置生成一个管道（旋转为默认值）
        GameObject pipes = Instantiate(prefab, transform.position, Quaternion.identity);

        // 在生成位置的基础上，向上或向下随机偏移高度
        pipes.transform.position += Vector3.up * Random.Range(minHeight, maxHeight);
    }
}
