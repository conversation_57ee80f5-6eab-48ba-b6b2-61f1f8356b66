// ==============================
// 文件：Logger.cs（通用）
// 作用：统一日志入口（Lua/框架/项目都可复用）
// 特性：Editor 下彩色前缀；可选写入文件（简单示例）
// ==============================
using System;
using System.IO;
using UnityEngine;

namespace CommonFramework.Utils
{
    public static class Logger
    {
        private static readonly string LogFile = Path.Combine(Application.persistentDataPath, "common_log.txt");

        public static void Log(string msg)
        {
#if UNITY_EDITOR
            Debug.Log($"<color=#5bc0de>[CF]</color> {msg}");
#else
            Debug.Log($"[CF] {msg}");
#endif
            TryAppend("[I] " + msg);
        }

        public static void LogWarning(string msg)
        {
            Debug.LogWarning($"[CF] {msg}");
            TryAppend("[W] " + msg);
        }

        public static void LogError(string msg)
        {
            Debug.LogError($"[CF] {msg}");
            TryAppend("[E] " + msg);
        }

        private static void TryAppend(string line)
        {
            try
            {
                File.AppendAllText(LogFile, $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} {line}\n");
            }
            catch { /* 忽略文件 IO 失败 */ }
        }
    }
}
