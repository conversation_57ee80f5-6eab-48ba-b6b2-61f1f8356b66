// ==============================
// 文件：CustomLuaCallCS.cs（通用白名单）
// 作用：声明哪些 C# 类型允许被 Lua 侧直接访问（LuaCallCSharp）
//      声明哪些 Lua 函数签名会被 C# 持有为委托（CSharpCallLua）
// 注意：项目侧如需扩展，请新建 ProjectCustomLuaCallCS.cs，不要改此通用文件
// ==============================
using System;
using System.Collections.Generic;
using UnityEngine;
using XLua;

namespace CommonFramework.Lua
{
    public static class CustomLuaCallCS
    {
        // ========== Lua 调 C# 的类型 ==========
        [LuaCallCSharp]
        public static List<Type> LuaCallCSharpTypes = new()
        {
            // 常用 System
            typeof(object),
            typeof(string),
            typeof(Action),
            typeof(Action<>),
            typeof(Action<,>),
            typeof(Func<>),
            typeof(Func<,>),

            // UnityEngine 常用类型
            typeof(Debug),
            typeof(Time),
            typeof(GameObject),
            typeof(Transform),
            typeof(Vector2),
            typeof(Vector3),
            typeof(Quaternion),
            typeof(WaitForSeconds),

            // 通用框架类型
            typeof(CommonFramework.AssetBundles.LuaAssetBridge),
            typeof(CommonFramework.Utils.Logger),
        };

        // ========== C# 持有 Lua 委托的签名（AOT 平台必须声明） ==========
        [CSharpCallLua]
        public interface LuaAction { void Invoke(); }

        [CSharpCallLua]
        public delegate void LuaAction1<T>(T arg);

        [CSharpCallLua]
        public delegate void LuaAction2<T1, T2>(T1 a, T2 b);

        [CSharpCallLua]
        public static List<Type> CSharpCallLuaTypes = new()
        {
            typeof(LuaAction),
            typeof(LuaAction1<>),
            typeof(LuaAction2<,>),
            typeof(Action),
            typeof(Action<>),
            typeof(Action<,>)
        };
    }
}
