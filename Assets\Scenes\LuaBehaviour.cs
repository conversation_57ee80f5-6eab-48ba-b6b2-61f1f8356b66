/*
*Copyright(C) by SWM
*Author: <PERSON> (updated by ChatGPT)
*Version: 4.4.4 (Free-only DOTween)
*UnityVersion：2018.4.23f1+
*Date: 2020-10-12 11:25:01 (Updated)
*/
using System;
using System.Collections.Generic;
using DG.Tweening;                       // 引用 DOTween 核心命名空间（仅 Free 版需要的类型）
using UnityEngine;
using UnityEngine.Video;                 // 若不需要可移除
using XLua;

namespace Samuel.RichWidget
{
    /// <summary>
    /// xLua 白名单：Lua 调 C# 的可访问类型
    /// ——仅保留 DOTween Free 版内置/可选模块的类型，删除所有 Pro 专属类型
    /// </summary>
    public static class CustomLuaCallCS
    {
        // 允许 Lua 侧直接访问的 C# 类型列表
        [LuaCallCSharp]
        public static IEnumerable<Type> mymodule_lua_call_cs_list = new List<Type>()
        {
            // ===== DOTween Free 核心 =====
            typeof(DG.Tweening.DOTween),                 // DOTween 静态入口
            typeof(DG.Tweening.Tween),                   // 基类：补间
            typeof(DG.Tweening.Tweener),                 // 具体补间
            typeof(DG.Tweening.Sequence),                // 序列
            typeof(DG.Tweening.TweenParams),             // 参数构建
            typeof(DG.Tweening.TweenCallback),           // 回调委托
            typeof(DG.Tweening.TweenSettingsExtensions), // 常用扩展
            typeof(DG.Tweening.ShortcutExtensions),      // Transform/Material 等快捷扩展
            typeof(DG.Tweening.EaseFactory),             // 自定义缓动
            typeof(DG.Tweening.DOVirtual),               // 虚拟 tween（用于数值驱动）
            typeof(DG.Tweening.DOTweenCYInstruction),    // 协程等待指令（WaitForCompletion 等）
            typeof(DG.Tweening.Core.ABSSequentiable),    // 序列基类（Lua 端少用，但无害）

            // ===== DOTween Free 可选模块（需在 DOTween Utility Panel 里勾选对应模块）=====
            typeof(DG.Tweening.DOTweenModuleUI),         // UI 模块（Text/Graphic 等）
            typeof(DG.Tweening.DOTweenModulePhysics),    // 3D 物理
            typeof(DG.Tweening.DOTweenModulePhysics2D),  // 2D 物理
            typeof(DG.Tweening.DOTweenModuleSprite),     // Sprite 模块
            typeof(DG.Tweening.DOTweenModuleUnityVersion),// Unity 版本兼容
            typeof(DG.Tweening.DOTweenModuleUtils),      // 工具类

            // ===== 其他常用 Unity 类型（按需保留/增删）=====
            typeof(RenderSettings),
            typeof(VideoPlayer),
            typeof(Animator),
            typeof(Animation),
            typeof(Material)
        };

        // 允许 C# 调用 Lua 委托的签名白名单
        [CSharpCallLua]
        public static IEnumerable<Type> mymodule_cs_call_lua_list = new List<Type>()
        {
            typeof(Action<float>),   // 形如 function(f) end
            typeof(Action<int>)      // 形如 function(i) end
        };
    }

    /// <summary>
    /// Inspector 注入表：把场景/对象引用传给 Lua 环境
    /// </summary>
    [Serializable]
    public class Injection
    {
        public string name;               // Lua 环境中使用的变量名
        public UnityEngine.Object value;  // 对应的 Unity 对象
    }

    /// <summary>
    /// 通用 Lua 行为承载组件：
    /// - 支持从 Resources 路径或直接 TextAsset 载入 Lua
    /// - 为每个脚本创建独立环境（隔离全局）
    /// - 自动分发 Awake/Start/Update/OnEnable/OnDisable/OnDestroy
    /// - 自定义 Loader：Resources 下按文件名/扩展名查找
    /// - 仅依赖 DOTween Free（移除一切 Pro 类型）
    /// </summary>
    public class LuaBehaviour : MonoBehaviour
    {
        [Header("Lua 脚本来源")]
        [SerializeField] private bool _loadFromResources = false; // 是否从 Resources 加载
        [SerializeField] private string _loadFromResourcesPath;   // Resources 相对路径（不带扩展）
        [SerializeField] private TextAsset _luaScript;            // 直接拖 TextAsset 也可

        [Header("Lua 注入参数（传入 _scriptEnv）")]
        [SerializeField] private Injection[] _injections;         // 传给 Lua 的对象

        // ===== xLua 全局（共享一个 LuaEnv）=====
        internal static LuaEnv luaEnv = new LuaEnv();             // 所有该组件共享同一 LuaEnv
        internal static float lastGCTime = 0f;                    // 上次 GC 时间
        internal const float GCInterval = 1f;                     // 每秒做一次 Tick

        // ===== Lua 回调缓存（减少每帧查表开销）=====
        private Action _luaStart;                                 // Lua Start()
        private Action _luaUpdate;                                // Lua Update()
        private Action _luaOnDestroy;                             // Lua OnDestroy()
        private Action _luaOnEnable;                              // Lua OnEnable()
        private Action _luaOnDisable;                             // Lua OnDisable()

        private LuaTable _scriptEnv;                              // 该脚本的独立环境

        // ========== Mono 生命周期 ==========
        private void Awake()
        {
            // 注册自定义 Loader（重复注册也安全，但可选做一次性优化）
            luaEnv.AddLoader(CustomLoaderFromXLuaResourcesPath);

            // 如选择从 Resources 加载，则按路径加载 TextAsset
            if (_loadFromResources)
            {
                // 允许传入不带扩展的路径，如 "Lua/flappy_bird"
                _luaScript = Resources.Load<TextAsset>(_loadFromResourcesPath);
            }

            // 没脚本则直接返回（组件可复用为“关闭状态”）
            if (_luaScript == null)
            {
                Debug.LogWarning($"[LuaBehaviour] 未找到 Lua 脚本：{name}");
                return;
            }

            // 为该脚本创建独立环境：_scriptEnv.__index = _G
            _scriptEnv = luaEnv.NewTable();
            LuaTable meta = luaEnv.NewTable();
            meta.Set("__index", luaEnv.Global); // 兜底到全局
            _scriptEnv.SetMetaTable(meta);
            meta.Dispose(); // 释放临时表

            // 注入 self（C# 侧实例）
            _scriptEnv.Set("self", this);

            // Inspector 注入到 Lua 环境
            if (_injections != null)
            {
                for (int i = 0; i < _injections.Length; i++)
                {
                    var inj = _injections[i];
                    if (!string.IsNullOrEmpty(inj.name))
                    {
                        _scriptEnv.Set(inj.name, inj.value);
                    }
                }
            }

            // 执行脚本文本：chunk 名使用 TextAsset 名称，便于错误定位
            luaEnv.DoString(_luaScript.text, _luaScript.name, _scriptEnv);

            // 缓存并调用 Lua 的 Awake（可选）
            var luaAwake = _scriptEnv.Get<Action>("Awake");
            luaAwake?.Invoke();

            // 缓存常用回调，避免每帧 Get
            _scriptEnv.Get("Start", out _luaStart);
            _scriptEnv.Get("Update", out _luaUpdate);
            _scriptEnv.Get("OnDestroy", out _luaOnDestroy);
            _scriptEnv.Get("OnEnable", out _luaOnEnable);
            _scriptEnv.Get("OnDisable", out _luaOnDisable);
        }

        private void Start()
        {
            if (_luaScript == null) return; // 无脚本直接跳过
            _luaStart?.Invoke();            // 分发 Lua Start
        }

        private void Update()
        {
            if (_luaScript == null) return; // 无脚本直接跳过

            // 分发 Lua Update
            _luaUpdate?.Invoke();

            // 定时驱动 Lua GC（Tick 内部会做增量 GC）
            if (Time.time - lastGCTime > GCInterval)
            {
                luaEnv.Tick();
                lastGCTime = Time.time;
            }
        }

        private void OnEnable()
        {
            if (_luaScript == null) return;
            _luaOnEnable?.Invoke();         // 分发 Lua OnEnable
        }

        private void OnDisable()
        {
            if (_luaScript == null) return;
            _luaOnDisable?.Invoke();        // 分发 Lua OnDisable
        }

        private void OnDestroy()
        {
            if (_luaScript == null) return;

            // 分发 Lua OnDestroy
            _luaOnDestroy?.Invoke();

            // 释放委托引用与表（避免循环引用）
            _luaOnDestroy = null;
            _luaUpdate = null;
            _luaStart = null;
            _luaOnEnable = null;
            _luaOnDisable = null;

            // 释放该脚本环境
            _scriptEnv?.Dispose();
            _scriptEnv = null;

            // 注入引用置空，便于 GC
            _injections = null;
        }

        // ========== 对外辅助 API ==========

        /// <summary>
        /// 无参调用 Lua 函数（从该脚本专属 _scriptEnv 里取）
        /// </summary>
        public void ExcuteLuaFunction(string functionName)
        {
            if (_luaScript == null || _scriptEnv == null) return;
            var fn = _scriptEnv.Get<Action>(functionName);
            fn?.Invoke();
        }

        /// <summary>
        /// 变参调用 Lua 函数（以 object[] 传入）
        /// </summary>
        public void ExcuteLuaFunction(string functionName, params object[] vs)
        {
            if (_luaScript == null || _scriptEnv == null) return;
            var fn = _scriptEnv.Get<Action<object[]>>(functionName);
            fn?.Invoke(vs);
        }

        /// <summary>
        /// 往 Lua 环境设置一个变量（用于运行时注入/覆盖）
        /// </summary>
        public void SetLuaValue<T>(string fieldName, T value)
        {
            if (_luaScript == null || _scriptEnv == null) return;
            Debug.Log($"[LuaBehaviour] Set : {fieldName} => {value}");
            _scriptEnv.Set(fieldName, value);
        }

        // ========== 自定义 Loader ==========
        /// <summary>
        /// xLua 自定义 Loader：从 Resources 里读取 lua 文本
        /// - 支持路径形如： "Lua/flappy_bird" 或 "Lua/pipesPrefab"
        /// - 自动尝试扩展名：".lua"、".lua.txt"、".txt"
        /// - 支持把传入 fileName 的 '.' 替换为 '/'（兼容 require 风格）
        /// </summary>
        public byte[] CustomLoaderFromXLuaResourcesPath(ref string fileName)
        {
            // 1) 统一路径分隔：a.b.c -> a/b/c
            string path = fileName.Replace('.', '/');

            // 2) 可能的候选（不带 Resources/ 前缀）
            //    注意：Resources.Load 不要带扩展名，但我们得尝试不同命名
            string[] candidates =
            {
                path,                // 直接按传入
                $"{path}.lua",       // 可能作者把扩展写进了资源名
                $"{path}.lua.txt",   // 常见做法
                $"{path}.txt"        // 兜底
            };

            // 3) 逐个尝试从 Resources 读取
            for (int i = 0; i < candidates.Length; i++)
            {
                TextAsset ta = Resources.Load<TextAsset>(candidates[i]);
                if (ta != null)
                {
                    return System.Text.Encoding.UTF8.GetBytes(ta.text);
                }
            }

            // 4) 兼容旧项目：固定加载 "Util.lua"
            TextAsset fallback = Resources.Load<TextAsset>("Util.lua");
            if (fallback != null)
            {
                return System.Text.Encoding.UTF8.GetBytes(fallback.text);
            }

            // 5) 未找到脚本
            //    返回 null 让 xLua 继续尝试其它 Loader（若有）
            Debug.LogWarning($"[LuaBehaviour] 自定义 Loader 未找到：{fileName}");
            return null;
        }
    }
}
