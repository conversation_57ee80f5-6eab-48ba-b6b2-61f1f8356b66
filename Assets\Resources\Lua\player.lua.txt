-- player.lua
-- 功能：Flappy Bird 小鸟控制（输入→速度→位移→倾斜 + 可选帧动画）
-- 说明：优先使用 Inspector 注入的参数与引用，未注入再使用默认与兜底逻辑

-- #region ========= C# 类型缓存 =========
local util            = require 'xlua.util'                         -- xLua 实用工具（协程生成）
local CS              = CS                                          -- xLua C# 入口
local Time            = CS.UnityEngine.Time                         -- Unity 时间（deltaTime）
local Vector3         = CS.UnityEngine.Vector3                      -- 三维向量
local Mathf           = CS.UnityEngine.Mathf                        -- 数学函数（Clamp等）
local Resources       = CS.UnityEngine.Resources                    -- 资源系统（LoadAll）
local WaitForSeconds  = CS.UnityEngine.WaitForSeconds               -- 协程用等待
local Debug           = CS.UnityEngine.Debug                        -- 日志
local GameObject      = CS.UnityEngine.GameObject                   -- GameObject 类型
local SpriteType      = CS.UnityEngine.Sprite                       -- Sprite 类型
local SpriteRendererT = CS.UnityEngine.SpriteRenderer               -- SpriteRenderer 类型
-- #endregion

-- #region ========= 模块状态（默认值 + 运行时缓存） =========
local M = {
    -- —— 可注入的数值（注入名用 n: 前缀；下同） ——
    strength      = 5.0,    -- 每次点击给予的向上初速度
    gravity       = -9.81,  -- 自定义“重力”（负数下落）
    tilt          = 5.0,    -- 速度→角度 系数
    maxTilt       = 45.0,   -- 最大倾斜角（绝对值）
    frameInterval = 0.15,   -- 翅膀动画帧间隔（秒）
    startY        = 0.0,    -- 启用时复位到的 Y 值

    -- —— 可注入的引用/配置 ——
    spriteAtlas   = "",     -- Resources 路径（例如 "Bird/Atlas"），为空则不加载
    obstacleTag   = "Obstacle", -- 障碍物标签（可注入覆盖）
    scoringTag    = "Scoring",  -- 计分触发区标签（可注入覆盖）

    -- —— 运行时缓存（不必注入） ——
    _sr           = nil,    -- SpriteRenderer
    _dir          = Vector3(0,0,0), -- 当前速度向量
    _sprites      = nil,    -- C# Sprite[] 动画帧
    _spriteIndex  = 0,      -- 当前帧索引（0 基）
    _animCo       = nil,    -- 协程句柄
}
-- #endregion

-- #region ========= 工具函数 =========
--- 将注入对象规范化为 SpriteRenderer（允许注入：SpriteRenderer 或其 GameObject）
---@param anyObj any
---@return UnityEngine.SpriteRenderer
local function asSpriteRenderer(anyObj)
    if not anyObj then return nil end                                -- 无则返回空
    if anyObj.GetType and anyObj:GetType() == SpriteRendererT then   -- 已是 SR
        return anyObj
    end
    local go = anyObj.gameObject and anyObj.gameObject or anyObj     -- 组件?.gameObject : 认为是GO
    if go and go.GetComponent then
        return go:GetComponent(typeof(SpriteRendererT))               -- 从GO取 SR
    end
    return nil
end

--- 安全设置当前显示的 sprite（若 _sr 与 _sprites 都有效）
local function applySprite(self)
    if self._sr and self._sprites and self._sprites.Length > 0 then   -- 前置检查
        -- 0 基索引：越界保护
        if self._spriteIndex < 0 or self._spriteIndex >= self._sprites.Length then
            self._spriteIndex = 0
        end
        self._sr.sprite = self._sprites[self._spriteIndex]            -- 切换贴图
    end
end
-- #endregion

-- #region ========= 生命周期：Awake / Start / OnEnable / OnDisable =========
function M:Awake()
    -- 1) 读取注入的数值（LuaBehaviour 会把 n:xxx 写入为同名变量）
    if strength      ~= nil then self.strength      = strength      end  -- 力度
    if gravity       ~= nil then self.gravity       = gravity       end  -- 重力
    if tilt          ~= nil then self.tilt          = tilt          end  -- 倾斜系数
    if maxTilt       ~= nil then self.maxTilt       = maxTilt       end  -- 最大倾角
    if frameInterval ~= nil then self.frameInterval = frameInterval end  -- 动画间隔
    if startY        ~= nil then self.startY        = startY        end  -- 初始Y

    -- 2) 读取注入的字符串/引用
    if obstacleTag   ~= nil and #tostring(obstacleTag) > 0 then
        self.obstacleTag = tostring(obstacleTag)                        -- 障碍物标签
    end
    if scoringTag    ~= nil and #tostring(scoringTag) > 0 then
        self.scoringTag  = tostring(scoringTag)                         -- 计分标签
    end
    if spriteAtlas   ~= nil then
        self.spriteAtlas = tostring(spriteAtlas)                        -- Resources 路径（可空）
    end
    if spriteRenderer ~= nil then
        self._sr = asSpriteRenderer(spriteRenderer)                     -- 注入的渲染器
    end

    -- 3) 若未注入 SpriteRenderer，则尝试从自身取
    if self._sr == nil then
        self._sr = self.gameObject:GetComponent(typeof(SpriteRendererT))
        if not self._sr then
            Debug.LogWarning("[Lua Player] 缺少 SpriteRenderer（建议在 Inspector 注入 spriteRenderer 或挂在含 SR 的物体上）")
        end
    end

    -- 4) 可选：加载动画帧（若提供了 Resources 路径）
    if self.spriteAtlas and #self.spriteAtlas > 0 then
        local arr = Resources.LoadAll(self.spriteAtlas, typeof(SpriteType)) -- 加载指定路径下全部 Sprite
        if arr and arr.Length > 0 then
            self._sprites = arr                                            -- 缓存数组（0 基）
            self._spriteIndex = 0                                          -- 从第 0 帧开始
            applySprite(self)                                              -- 应用初始帧
        else
            Debug.LogWarning("[Lua Player] 未在 Resources 加载到 Sprite 组：" .. self.spriteAtlas)
        end
    end
end

function M:Start()
    -- 5) 若有动画帧则开协程：按 frameInterval 轮播
    if self._sprites and self._sprites.Length > 0 and not self._animCo then
        self._animCo = self:StartCoroutine(util.cs_generator(function()
            while true do
                self._spriteIndex = self._spriteIndex + 1                 -- 下一帧
                if self._spriteIndex >= self._sprites.Length then         -- 循环回到 0
                    self._spriteIndex = 0
                end
                applySprite(self)                                         -- 切帧显示
                coroutine.yield(WaitForSeconds(self.frameInterval))        -- 等待下一次
            end
        end))
    end
end

function M:OnEnable()
    -- 6) 启用时复位位置与速度（仅改 Y）
    local p = self.transform.position                                     -- 取当前位置
    p.y = self.startY                                                     -- 应用起始Y（默认 0）
    self.transform.position = p                                           -- 写回
    self._dir = Vector3(0,0,0)                                            -- 速度清零
end

function M:OnDisable()
    -- 7) 停止动画协程（若在跑）
    if self._animCo then
        self:StopCoroutine(self._animCo)
        self._animCo = nil
    end
end
-- #endregion

-- #region ========= 每帧：输入 → 速度积分 → 位移 → 倾斜 =========
function M:Update()
    -- 1) 输入：空格 / 鼠标左键 / 触摸开始 → 给予向上初速度
    if CS.UnityEngine.Input.GetKeyDown(CS.UnityEngine.KeyCode.Space)      -- 键盘空格
        or CS.UnityEngine.Input.GetMouseButtonDown(0) then                 -- 鼠标左键
        self._dir = Vector3(0, self.strength, 0)                           -- 设定竖直速度
    else
        if CS.UnityEngine.Input.touchCount > 0 then                        -- 移动端触摸
            local t = CS.UnityEngine.Input.GetTouch(0)                     -- 取第一指
            if t.phase == CS.UnityEngine.TouchPhase.Began then             -- 触摸开始
                self._dir = Vector3(0, self.strength, 0)                   -- 设定竖直速度
            end
        end
    end

    -- 2) 重力：v += g * dt
    self._dir.y = self._dir.y + self.gravity * Time.deltaTime              -- 速度积分

    -- 3) 位移：x' = x + v * dt
    self.transform.position = self.transform.position + self._dir * Time.deltaTime

    -- 4) 倾斜：z角 = clamp(vy * tilt, [-maxTilt, maxTilt])
    local z = Mathf.Clamp(self._dir.y * self.tilt, -self.maxTilt, self.maxTilt)
    local e = self.transform.eulerAngles
    e.z = z
    self.transform.eulerAngles = e
end
-- #endregion

-- #region ========= 触发器：计分/死亡 =========
-- 注意：你的 LuaBehaviour.cs 目前仅转发 Awake/Start/Update/OnEnable/OnDisable/OnDestroy，
--       默认并不会转发 OnTriggerEnter2D。若要让本函数生效，请：
--       A) 继续用 C# Player 处理 2D 触发并在命中时调用 _G.GameManager；或
--       B) 在 LuaBehaviour.cs 里添加对 OnTriggerEnter2D 的转发（再调用 _fXX.Call）
function M:OnTriggerEnter2D(other)
    local go = other and other.gameObject or nil                           -- 保护性取值
    if not go then return end

    -- 命中障碍：GameOver
    if go:CompareTag(self.obstacleTag) then
        if _G.GameManager and _G.GameManager.GameOver then
            _G.GameManager:GameOver()
        end
        self.enabled = false                                               -- 立即禁用控制
        return
    end

    -- 穿过计分区：+1 分
    if go:CompareTag(self.scoringTag) then
        if _G.GameManager and _G.GameManager.IncreaseScore then
            _G.GameManager:IncreaseScore()
        end
        return
    end
end
-- #endregion

return M
