// ==============================
// 文件：LuaAssetBridge.cs（通用给 Lua 调用的桥）
// 作用：把 AB 的常用加载能力暴露给 Lua（回调式）
// Lua 侧调用示例：
//   local bridge = CS.GameObject.Find("Managers"):GetComponent(typeof(CS.CommonFramework.AssetBundle.LuaAssetBridge))
//   bridge:LoadPrefab("pipes_bundle", "PipesPrefab", function(go) if go then go.transform.position = CS.UnityEngine.Vector3(0,0,0) end end)
// ==============================
using System;
using UnityEngine;

namespace CommonFramework.AssetBundles
{
    [DisallowMultipleComponent]
    public class LuaAssetBridge : MonoBehaviour
    {
        public void LoadPrefab(string bundleName, string assetName, Action<GameObject> onLoaded)
        {
            if (!AssetBundleManager.TryGetInstance(out var abm)) { onLoaded?.Invoke(null); return; }
            abm.LoadAssetAsync<GameObject>(bundleName, assetName, prefab =>
            {
                if (prefab == null) { onLoaded?.Invoke(null); return; }
                var go = Instantiate(prefab);
                onLoaded?.Invoke(go);
            });
        }

        public void LoadTextAsset(string bundleName, string assetName, Action<string> onLoaded)
        {
            if (!AssetBundleManager.TryGetInstance(out var abm)) { onLoaded?.Invoke(null); return; }
            abm.LoadAssetAsync<TextAsset>(bundleName, assetName, ta =>
            {
                onLoaded?.Invoke(ta ? ta.text : null);
            });
        }

        public void UnloadBundle(string bundleName, bool unloadAllLoadedObjects = false)
        {
            if (!AssetBundleManager.TryGetInstance(out var abm)) return;
            abm.UnloadBundle(bundleName, unloadAllLoadedObjects);
        }

        // 便捷工具（Lua 常用）
        public void SetParent(Transform child, Transform parent, bool worldPositionStays = false)
        {
            if (child) child.SetParent(parent, worldPositionStays);
        }

        public void SetLayerRecursively(GameObject go, int layer)
        {
            if (!go) return;
            foreach (var tr in go.GetComponentsInChildren<Transform>(true))
                tr.gameObject.layer = layer;
        }
    }
}
