/*
 * 该脚本为Unity编辑器扩展工具，用于将 Assets/Lua 目录下的所有 .lua 和 .lua.txt 文件
 * 复制到 Application.persistentDataPath/lua 目录下，便于移动端热更或运行时加载Lua脚本。
 * 使用方式：在Unity菜单栏 Tools 下点击“Copy Lua to Persistent”即可执行。
 */

using UnityEngine;
using UnityEditor;
using System.IO;

/// <summary>
/// 编辑器工具类：将Assets/Lua目录下的Lua脚本复制到持久化目录
/// </summary>
public class CopyLuaToPersistent
{
    /// <summary>
    /// 在Unity菜单栏添加“Tools/Copy Lua to Persistent”菜单项
    /// </summary>
    [MenuItem("Tools/Copy Lua to Persistent")]
    public static void CopyLua()
    {
        // 源目录：Assets/Lua
        string srcRoot = Path.Combine(Application.dataPath, "Lua"); // Assets/Lua
        if (!Directory.Exists(srcRoot))
        {
            // 如果找不到源目录，输出警告并返回
            Debug.LogWarning("没有找到 Assets/Lua 目录");
            return;
        }

        // 目标目录：持久化路径/lua
        string dstRoot = Path.Combine(Application.persistentDataPath, "lua");
        // 如果目标目录不存在则创建
        if (!Directory.Exists(dstRoot)) Directory.CreateDirectory(dstRoot);

        // 获取所有文件（包括子目录）
        var files = Directory.GetFiles(srcRoot, "*.*", SearchOption.AllDirectories);
        int copied = 0;
        foreach (var f in files)
        {
            // 只处理 .lua 和 .lua.txt 文件
            if (!f.EndsWith(".lua") && !f.EndsWith(".lua.txt")) continue;
            // 计算相对路径，保持目录结构
            string rel = f.Substring(srcRoot.Length + 1); // 相对路径
            // 如果是 .lua.txt，目标文件名去掉 .txt
            if (rel.EndsWith(".lua.txt"))
            {
                rel = rel.Substring(0, rel.Length - 4); // 去掉 .txt
            }
            string dst = Path.Combine(dstRoot, rel);
            // 确保目标文件夹存在
            Directory.CreateDirectory(Path.GetDirectoryName(dst));
            // 复制文件，若已存在则覆盖
            File.Copy(f, dst, true);
            copied++;
        }
        // 输出复制结果
        Debug.Log($"CopyLuaToPersistent: copied {copied} files to {dstRoot}");
    }
}
