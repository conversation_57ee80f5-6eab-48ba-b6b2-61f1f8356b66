// ==============================
// 文件：LuaManager.cs（通用）
// 作用：全局 LuaEnv 的创建/维护、注册自定义 Loader、统一 GC 与错误处理
// 依赖：xLua（Runtime），LuaLoader（本框架内）、Logger（本框架内）
// 放置：Assets/CommonFramework/Runtime/Lua/
// ==============================
using System;
using UnityEngine;
using XLua;

namespace CommonFramework.Lua
{
    /// <summary>
    /// LuaManager：全局唯一的 Lua 环境管理器（建议场景中放一个 Managers 物体挂此脚本）
    /// </summary>
    [DisallowMultipleComponent]
    public class LuaManager : MonoBehaviour
    {
        // 单例：全局访问入口（通用框架中仅暴露只读实例）
        public static LuaManager Instance { get; private set; }

        // xLua 的虚拟机（VM）
        public LuaEnv Env { get; private set; }

        // GC 步进间隔（秒）：避免每帧 full gc，默认每 1 秒做一次增量 gc
        [SerializeField, Min(0.1f)] private float gcInterval = 1f;
        private float _gcTimer;

        // 入口模块名：可选——开发期用于自动 require main
        [SerializeField] private string entryModule = "main";

        // 是否在 Awake 时自动执行入口模块
        [SerializeField] private bool runEntryOnAwake = true;

        // Loader 配置引用（可选）：若场景中存在 LuaLoader 单例，会使用它；否则使用静态默认实现
        [SerializeField] private LuaLoader loaderConfig;

        private void Awake()
        {
            // 保证单例唯一
            if (Instance != null && Instance != this) { Destroy(gameObject); return; }
            Instance = this;
            DontDestroyOnLoad(gameObject);

            // 创建 LuaEnv
            Env = new LuaEnv();

            // 注册自定义 loader：xLua 支持多个 loader；这里统一走 LuaLoader.GetBytes
            Env.AddLoader((ref string filepath) =>
            {
                // 统一入口：优先 persistent，其次 AB（TextAsset），最后 Resources
                return (loaderConfig != null ? loaderConfig : LuaLoader.Default).GetBytes(filepath);
            });

            // 可选：设置全局打印函数映射到我们的 Logger
            // 这样 Lua 侧 print() -> Unity 控制台（带前缀）
            Env.Global.Set("cs_log", new Action<string>(CommonFramework.Utils.Logger.Log));
            Env.DoString("print = function(...) cs_log(table.concat({ ... }, '\\t')) end", "override_print", Env.Global);

            // 自动运行入口模块（开发期方便）
            if (runEntryOnAwake && !string.IsNullOrEmpty(entryModule))
            {
                SafeRequire(entryModule);
            }
        }

        private void Update()
        {
            // 让 xLua 驱动内部 GC/定时器（官方建议每帧 Tick）
            Env?.Tick();

            // 简单的增量 GC：每 gcInterval 秒触发一次
            _gcTimer += Time.unscaledDeltaTime;
            if (_gcTimer >= gcInterval)
            {
                _gcTimer = 0f;
                try { Env?.FullGc(); } catch (Exception e) { CommonFramework.Utils.Logger.LogError($"Lua GC error: {e}"); }
            }
        }

        private void OnDestroy()
        {
            // 释放 LuaEnv（在退出或切换进程时调用）
            try { Env?.Dispose(); } catch (Exception e) { CommonFramework.Utils.Logger.LogError($"Dispose LuaEnv error: {e}"); }
            Env = null;
            if (Instance == this) Instance = null;
        }

        /// <summary>
        /// 安全执行 require（捕获异常并输出栈）
        /// </summary>
        public bool SafeRequire(string module)
        {
            try
            {
                Env.DoString($"require('{module}')", $"require:{module}", Env.Global);
                return true;
            }
            catch (Exception e)
            {
                CommonFramework.Utils.Logger.LogError($"Lua require '{module}' failed:\n{e}");
                return false;
            }
        }

        /// <summary>
        /// 安全执行一段 Lua 代码（用于调试或热更临时脚本）
        /// </summary>
        public bool SafeDoString(string code, string chunk = "chunk")
        {
            try
            {
                Env.DoString(code, chunk, Env.Global);
                return true;
            }
            catch (Exception e)
            {
                CommonFramework.Utils.Logger.LogError($"Lua DoString error ({chunk}):\n{e}");
                return false;
            }
        }
    }
}
