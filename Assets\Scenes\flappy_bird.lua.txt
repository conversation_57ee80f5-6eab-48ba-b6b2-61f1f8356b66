-- 引入 xlua 提供的工具模块
local util = require 'xlua.util'

-- 缓存 Unity 常用类，减少每次调用跨语言的性能消耗
local UnityEngine = CS.UnityEngine
local Vector3 = CS.UnityEngine.Vector3
local Vector2 = CS.UnityEngine.Vector2
local UI = CS.UnityEngine.UI
local Time = CS.UnityEngine.Time
local DOTween = CS.DG.Tweening.DOTween
local Instantiate = CS.UnityEngine.GameObject.Instantiate
local ButtonClickedEvent = CS.UnityEngine.UI.Button.ButtonClickedEvent
local GameObject = CS.UnityEngine.GameObject

-- ↓↓↓ 相当于 Player.cs 里的成员变量
local direction = nil            -- 鸟的移动方向向量（C#里是 Vector3 direction）
local gravity = -9.8             -- 重力加速度（C# 里是 public float gravity = -9.81f;）
local strength = 5               -- 点击时向上的初速度（C# 里是 public float strength = 5f;）
local spriteRenderer = nil       -- 精灵渲染组件（C# 里是 private SpriteRenderer spriteRenderer）
local sprites = nil              -- 鸟的动画帧数组（C# 里是 public Sprite[] sprites）
local spriteIndex = 0            -- 当前显示的动画帧索引（C# 里是 private int spriteIndex）

-- ↓↓↓ 相当于 Parallax.cs 的成员
local meshRenderer_background = nil -- 背景的 MeshRenderer
local meshRenderer_ground = nil     -- 地面的 MeshRenderer
local animationSpeed_background = 0.05 -- 背景滚动速度
local animationSpeed_ground = 1       -- 地面滚动速度

-- ↓↓↓ 相当于 Spawner.cs 的成员
local spawnRate = 1          -- 生成管道的间隔（public float spawnRate）
local minHeight = -1         -- 管道随机生成最小高度
local maxHeight = 2
local sequence_spawn = nil   -- DOTween 定时生成的计时器（替代 C# InvokeRepeating）

-- ↓↓↓ 相当于 Pipes.cs 的成员
local speed_pipes = 5        -- 管道移动速度
local leftEdge = nil         -- 屏幕左边界（用于销毁管道）

-- ↓↓↓ 相当于 GameManager.cs 的成员
local score = 0              -- 分数
local clicked = false        -- 游戏是否开始（控制 Update 是否执行逻辑）
local isGameOver = false     -- 游戏结束标记

-- 额外变量（Lua 里新增）
local collider_bird = false  -- 鸟的碰撞体
local gb_hit = nil           -- 碰撞到的对象
local scoreSpaceUp = nil     -- 得分区上边界
local scoreSpaceDown = nil   -- 得分区下边界
local cachedPipes = {}       -- 管道对象池

-- 构建对象池数据结构（原 C# 没有，Lua 版本新增优化）
function CachePipes(clone)
    local pipe = clone.transform
    local item = {}
    item.gameObject = clone
    item.top = pipe:Find('Top Pipe')
    item.bottom = pipe:Find('Bottom Pipe')
    item.scorezone = pipe:Find('Scoring Zone')
    return item
end

-- Awake：相当于 C# MonoBehaviour.Awake()
function Awake()
    -- C#：设置帧率、初始化、暂停
    -- Application.targetFrameRate = 60;
    Pause()
end

-- Start：相当于 C# MonoBehaviour.Start()
function Start()
    -- 计算屏幕左边界（和 Pipes.cs Start() 一样）
    leftEdge = UnityEngine.Camera.main:ScreenToWorldPoint(Vector3.zero).x - 1
end

-- OnEnable：相当于 C# MonoBehaviour.OnEnable()
function OnEnable()
    -- 获取组件（等价于 C# 的 GetComponent<>）
    if spriteRenderer == nil then spriteRenderer = Bird:GetComponent('SpriteRenderer') end
    if meshRenderer_background == nil then meshRenderer_background = Background:GetComponent('MeshRenderer') end
    if meshRenderer_ground == nil then meshRenderer_ground = Ground:GetComponent('MeshRenderer') end
    if sprites == nil then
        sprites = {Sprites1, Sprites2, Sprites3}
    end
    RegisterButton(playBtn, PlayGame) -- 给按钮绑定点击事件
    collider_bird = Bird:GetComponent('CircleCollider2D')
    clicked = true
end

-- Update：相当于 C# MonoBehaviour.Update()
function Update()
    if clicked then
        -- 点击控制（等价 Player.cs Update() 中的 Input 检测）
        if UnityEngine.Input.GetKeyDown(UnityEngine.KeyCode.Space) or UnityEngine.Input.GetMouseButtonDown(0) then
            direction = Vector3.up * strength
        end
        -- 移动端点击
        if UnityEngine.Input.touchCount > 0 then
            local touch = UnityEngine.Input.GetTouch(0)
            if touch.phase == UnityEngine.TouchPhase.Began then
                direction = Vector3.up * strength
            end
        end

        -- 重力与位置更新（等价 Player.cs 中的方向计算）
        if direction ~= nil then
            direction.y = direction.y + gravity * Time.deltaTime
            Bird.transform.position = Bird.transform.position + direction * Time.deltaTime
        end

        -- 背景与地面滚动（等价 Parallax.cs Update()）
        if meshRenderer_background ~= nil then
            meshRenderer_background.material.mainTextureOffset =
                meshRenderer_background.material.mainTextureOffset + Vector2(animationSpeed_background * Time.deltaTime, 0)
        end
        if meshRenderer_ground ~= nil then
            meshRenderer_ground.material.mainTextureOffset =
                meshRenderer_ground.material.mainTextureOffset + Vector2(animationSpeed_ground * Time.deltaTime, 0)
        end

        -- 管道移动与碰撞检测（融合 Pipes.cs Update() 和 Player.cs OnTriggerEnter2D()）
        if cachedPipes ~= nil then
            for i = 1, #cachedPipes do
                if cachedPipes[i].gameObject.activeSelf then
                    cachedPipes[i].gameObject.transform.position =
                        cachedPipes[i].gameObject.transform.position + Vector3.left * speed_pipes * Time.deltaTime

                    -- 通过位置范围判断是否得分或撞击
                    if cachedPipes[i].gameObject.transform.position.x > -0.1 and
                       cachedPipes[i].gameObject.transform.position.x < 0.1 then
                        scoreSpaceUp = cachedPipes[i].scorezone.position.y + 1
                        scoreSpaceDown = cachedPipes[i].scorezone.position.y - 1
                        if Bird.transform.position.y > scoreSpaceDown and Bird.transform.position.y < scoreSpaceUp then
                            IncreaseScore() -- 等价 C# GameManager.IncreaseScore()
                        elseif not isGameOver then
                            isGameOver = true
                            if sequence_spawn ~= nil then sequence_spawn:Kill() end
                            GameOver()
                        end
                    end

                    -- 超出屏幕回收
                    if cachedPipes[i].gameObject.transform.position.x < leftEdge then
                        cachedPipes[i].gameObject:SetActive(false)
                    end
                end
            end
        end
    end
end

-- OnDisable：相当于 C# MonoBehaviour.OnDisable()
function OnDisable()
    clicked = false
    sequence_spawn:Kill() -- 替代 C# CancelInvoke(nameof(Spawn))
end

-- AnimateSprite：等价 Player.cs 中的 AnimateSprite()
function AnimateSprite()
    spriteIndex = spriteIndex + 1
    if spriteIndex > #sprites then spriteIndex = 1 end
    spriteRenderer.sprite = sprites[spriteIndex]
end

-- Spawn：等价 Spawner.cs 中的 Spawn()
function Spawn()
    if GetPipes() == nil then
        local pipes = Instantiate(prefab, Spawner.transform.position, UnityEngine.Quaternion.identity)
        pipes.transform.position = pipes.transform.position + Vector3.up * math.random(minHeight, maxHeight)
        table.insert(cachedPipes, CachePipes(pipes))
    else
        local pipes = GetPipes()
        pipes.gameObject.transform.position = Spawner.transform.position
        pipes.gameObject.transform.position =
            pipes.gameObject.transform.position + Vector3.up * math.random(minHeight, maxHeight)
        pipes.gameObject:SetActive(true)
    end
end

-- GetPipes：Lua 对象池获取可复用管道（原 C# 没有）
function GetPipes()
    if cachedPipes ~= nil and #cachedPipes > 0 then
        for i = 1, #cachedPipes do
            if not cachedPipes[i].gameObject.activeSelf then
                return cachedPipes[i]
            end
        end
    end
    return nil
end

-- PlayGame：融合 C# GameManager.Play() 的逻辑
function PlayGame()
    isGameOver = false
    clicked = true
    score = 0
    scoreText.text = tostring(score)
    playBtn.gameObject:SetActive(false)
    gameOver:SetActive(false)
    Time.timeScale = 1

    -- 清空管道
    if cachedPipes ~= nil and #cachedPipes > 0 then
        for i = #cachedPipes, 1, -1 do
            GameObject.DestroyImmediate(cachedPipes[i].gameObject)
        end
        cachedPipes = {}
    end

    -- 重置鸟位置
    local position = Bird.transform.position
    position.y = 0
    Bird.transform.position = position
    direction = Vector3.zero

    -- 设置鸟动画循环
    if sequence ~= nil then sequence:Kill() end
    sequence = DOTween.Sequence()
    sequence:AppendCallback(AnimateSprite)
    sequence:AppendInterval(0.15)
    sequence:SetLoops(-1)

    -- 设置管道生成循环
    if sequence_spawn ~= nil then sequence_spawn:Kill() end
    sequence_spawn = DOTween.Sequence()
    sequence_spawn:AppendCallback(Spawn)
    sequence_spawn:AppendInterval(spawnRate)
    sequence_spawn:SetLoops(-1)
end

-- GameOver：等价 C# GameManager.GameOver()
function GameOver()
    playBtn.gameObject:SetActive(true)
    gameOver:SetActive(true)
    Pause()
    clicked = false
end

-- Pause：等价 C# GameManager.Pause()
function Pause()
    Time.timeScale = 0
    clicked = false
end

-- IncreaseScore：等价 C# GameManager.IncreaseScore()
function IncreaseScore()
    score = score + 1
    scoreText.text = tostring(score)
end

-- 按钮注册封装
function RegisterButton(btn, func)
    btn.onClick:AddListener(func)
end
