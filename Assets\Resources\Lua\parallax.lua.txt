-- uv_scroll.lua
-- 功能：通过修改材质的 mainTextureOffset 实现 UV 水平滚动（跑马灯/流水材质效果）
-- 说明：优先使用 Inspector 注入的 material 或 meshRenderer；都没注入则从自身对象取 MeshRenderer
-- 返回表 M，兼容 CommonFramework 的 LuaBehaviour（将生命周期转发到表方法）

-- #region ========= C# 类型缓存 =========
local CS      = CS                                               -- xLua 的 C# 入口
local Vector2 = CS.UnityEngine.Vector2                           -- 二维向量（用于偏移增量）
local Time    = CS.UnityEngine.Time                              -- Unity 时间（取 deltaTime）
local Debug   = CS.UnityEngine.Debug                             -- 日志输出
local OBJ     = CS.UnityEngine.Object                            -- UnityEngine.Object 基类（用于类型判断）
local MeshRendererType = CS.UnityEngine.MeshRenderer             -- MeshRenderer 类型缓存
-- #endregion

-- #region ========= 模块状态与可注入配置 =========
local M = {
    animationSpeed = 1.0,    -- 默认滚动速度（可被 Inspector 注入的同名变量覆盖）
    _mr            = nil,    -- 运行时缓存：MeshRenderer
    _mat           = nil,    -- 运行时缓存：Material（若直接注入 material，则优先用它）
}
-- #endregion

-- #region ========= 工具函数 =========
--- 将注入的对象规范化为 MeshRenderer（允许注入的是：MeshRenderer 或其 GameObject）
---@param anyObj any
---@return UnityEngine.MeshRenderer
local function asMeshRenderer(anyObj)
    -- 若没有就返回 nil
    if anyObj == nil then return nil end
    -- 若本身就是 MeshRenderer，直接返回
    if anyObj.GetType and anyObj:GetType() == MeshRendererType then
        return anyObj
    end
    -- 若可能是组件，尝试取 gameObject；否则认为它就是 GameObject
    local go = anyObj.gameObject and anyObj.gameObject or anyObj
    -- 若有 GetComponent，则尝试拿 MeshRenderer
    if go and go.GetComponent then
        return go:GetComponent(typeof(MeshRendererType))
    end
    return nil
end
-- #endregion

-- #region ========= 生命周期：Awake =========
function M:Awake()
    -- 1) 处理可注入：animationSpeed
    --    Inspector里若注入了同名变量 animationSpeed（float），此处会覆盖默认值
    if animationSpeed ~= nil then
        self.animationSpeed = animationSpeed
    end

    -- 2) 处理可注入：material（Material）
    --    若直接注入了材质，则优先使用它（注意：运行时改 material 会实例化材质）
    if material ~= nil then
        self._mat = material
    end

    -- 3) 处理可注入：meshRenderer（MeshRenderer 或 GameObject）
    --    若注入了渲染器引用，则规范为 MeshRenderer
    if meshRenderer ~= nil then
        self._mr = asMeshRenderer(meshRenderer)
    end

    -- 4) 若仍未拿到 MeshRenderer，尝试从自身物体上获取
    if self._mr == nil then
        self._mr = self.gameObject:GetComponent(typeof(MeshRendererType))
    end

    -- 5) 若未注入 material，且拿到了 MeshRenderer，则用其 material
    --    注意：访问 .material 会克隆一份材质实例；若你想改共享材质，请改 .sharedMaterial
    if self._mat == nil and self._mr ~= nil then
        self._mat = self._mr.material
    end

    -- 6) 基本可用性检查并告警
    if self._mr == nil then
        Debug.LogWarning("[Lua][UVScroll] 未找到 MeshRenderer（请在 Inspector 注入 meshRenderer 或将脚本挂在含有 MeshRenderer 的物体上）")
    end
    if self._mat == nil then
        Debug.LogWarning("[Lua][UVScroll] 未找到 Material（请在 Inspector 注入 material 或确保 MeshRenderer 上有材质）")
    end
end
-- #endregion

-- #region ========= 生命周期：Update（逐帧滚动） =========
function M:Update()
    -- 1) 没有材质就不做任何计算，避免空引用
    if not self._mat then return end

    -- 2) 取 deltaTime（受 timeScale 影响；若你希望暂停仍滚动，可改用 Time.unscaledDeltaTime）
    local dt = Time.deltaTime

    -- 3) 计算新的 UV 偏移：X 轴滚动（向右为正，向左为负）
    --    这里使用 mainTextureOffset（等价于修改 _MainTex 的 ST 中的偏移）
    local curr = self._mat.mainTextureOffset
    local next = curr + Vector2(self.animationSpeed * dt, 0)

    -- 4) 写回材质偏移
    self._mat.mainTextureOffset = next
end
-- #endregion

return M
