﻿using UnityEngine; // 引入 Unity 引擎命名空间，提供 Transform、Camera、Vector3 等类

public class Pipes : MonoBehaviour
{
    // 上方管道的 Transform（用于在编辑器里挂载引用，方便调节位置/碰撞）
    public Transform top;

    // 下方管道的 Transform
    public Transform bottom;

    // 管道水平移动的速度（单位：世界单位/秒）
    public float speed = 5f;

    // 屏幕左边界的 X 坐标（超出则销毁管道）
    private float leftEdge;

    // Start 在脚本启用的第一帧调用
    private void Start()
    {
        // 获取屏幕左下角（0,0）对应的世界坐标的 x 值
        // 再向左偏移 1 个单位，让销毁点在屏幕外，避免刚到边缘就消失
        leftEdge = Camera.main.ScreenToWorldPoint(Vector3.zero).x - 1f;
    }

    // Update 每帧调用一次
    private void Update()
    {
        // 让管道向左移动：Vector3.left 表示 (-1,0,0) 方向
        // speed 决定移动速度，Time.deltaTime 保证帧率无关
        transform.position += Vector3.left * speed * Time.deltaTime;

        // 如果管道的 X 坐标小于屏幕左边界，说明完全看不见了
        if (transform.position.x < leftEdge)
        {
            // 销毁当前管道对象，释放内存
            Destroy(gameObject);
        }
    }
}
