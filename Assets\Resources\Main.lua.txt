-- Assets/Resources/Lua/main.lua.txt
-- 项目入口 main.lua
-- 说明：LuaManager 在 Awake 时会自动 require("main")（可在 LuaManager Inspector 关闭）
-- 该文件尽量只做入口初始化、require 其它模块并提供热更 / reload 工具

local util = {} -- 简单工具表（可扩展）

-- 安全 require，避免单个模块出错把整个入口打掉
local function safe_require(name)
    local ok, mod = pcall(require, name)
    if not ok then
        print(string.format("[Lua][main] require '%s' failed: %s", name, tostring(mod)))
        return nil
    end
    return mod
end

-- 简单的热重载（清除 package.loaded 再 require）
function util.reload(moduleName)
    if package.loaded[moduleName] ~= nil then
        package.loaded[moduleName] = nil
    end
    return safe_require(moduleName)
end

-- 将模块暴露到全局方便调试（注意：生产环境谨慎使用全局污染）
local function expose_global(name, tbl)
    if tbl ~= nil then
        rawset(_G, name, tbl)
        print(string.format("[Lua][main] expose global: %s", name))
    end
end

-- 1) 首先尝试加载游戏核心逻辑的 Lua 模块（如果你已经把 GameManager 写成 Lua）
local gmLua = safe_require("game_manager") -- 如果你把 game_manager.lua 放在 Resources/Lua 下或在 loader 可访问处
if gmLua then
    expose_global("GameManagerLua", gmLua)
    -- 如果模块里有 Awake 或 Init，尝试调用它（保持兼容 C# 生命周期）
    if type(gmLua.Awake) == "function" then
        -- 把全局 env/self 传入（如果你的模块需要）
        pcall(function() gmLua:Awake() end)
    elseif type(gmLua.Init) == "function" then
        pcall(function() gmLua.Init(gmLua) end)
    end
else
    print("[Lua][main] no game_manager.lua found (ok if you use C# GameManager)")
end

-- 2) 如果没有 Lua 版的 GameManager，绑定 C# 的 GameManager 实例到 Lua（用于在 Lua 中调用 C# 方法）
-- 注意：C# 的 GameManager 不是单例类型，这里用 FindObjectOfType 查找场景中的实例（如果存在）
local OK, cs = pcall(function()
    return CS.UnityEngine.Object.FindObjectOfType(CS.GameManager)
end)
if OK and cs ~= nil then
    rawset(_G, "GameManagerCS", cs)
    print("[Lua][main] bound GameManagerCS (C# instance) to _G.GameManagerCS")
else
    print("[Lua][main] no C# GameManager instance found in scene")
end

-- 3) 尝试加载其它可选模块（管道、spawner、player、parallax 等），失败就忽略
local optModules = { "spawner", "pipes", "player", "parallax"  }
for _, m in ipairs(optModules) do
    local mod = safe_require(m)
    if mod then
        expose_global(m:sub(1,1):upper() .. m:sub(2) .. "Lua", mod) -- 如 spawner -> SpawnerLua
    end
end

-- 4) 常用开发工具函数（日志/检查 package 路径）
function util.printLoaded()
    for k, v in pairs(package.loaded) do
        if v ~= nil then
            print("[Lua][main] loaded:", k)
        end
    end
end

function util.printSearchPaths()
    print("[Lua][main] package.path:", package.path)
    print("[Lua][main] package.cpath:", package.cpath)
end

-- 暴露 util 到全局，方便在 REPL / 控制台调用
_G.LuaDev = util

print("[Lua][main] initialized. use LuaDev.reload('module_name') to hot-reload.")
