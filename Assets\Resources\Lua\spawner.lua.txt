-- spawner.lua
-- 功能：按固定间隔生成“管道/障碍物”，并在生成时给一个随机的垂直偏移
-- 用法：把本脚本挂到“生成器”物体（含位置），通过 Inspector 注入预制体与参数

-- #region ========= C# 类型缓存 =========
local util            = require 'xlua.util'                     -- 引入 xLua 的工具（用来创建 C# 协程）
local CS              = CS                                      -- xLua 访问 C# 的入口
local Object          = CS.UnityEngine.Object                   -- 用于实例化/销毁
local Vector3         = CS.UnityEngine.Vector3                  -- 三维向量（这里用到 Vector3.up）
local Random          = CS.UnityEngine.Random                   -- Unity 的随机数工具
local WaitForSeconds  = CS.UnityEngine.WaitForSeconds           -- 协程等待（受 Time.timeScale 影响）
local Debug           = CS.UnityEngine.Debug                    -- 日志
-- #endregion

-- #region ========= 模块状态（可注入项 + 运行时缓存） =========
local M = {
    prefab     = nil,     -- 【对象注入】要生成的预制体（GameObject）
    spawnRate  = 1.0,     -- 【数值注入 n:spawnRate】生成间隔（秒）
    minHeight  = -1.0,    -- 【数值注入 n:minHeight】随机高度下界
    maxHeight  =  2.0,    -- 【数值注入 n:maxHeight】随机高度上界

    _co        = nil,     -- 运行时：协程句柄
    _parent    = nil,     -- 【可选对象注入】spawnParent：生成出来的物体设为其子节点
}
-- #endregion

-- #region ========= 工具函数 =========
--- 实际生成逻辑：实例化预制体、设置父子关系、随机高度并放置到生成器位置
---@param self table
local function spawn(self)
    if not self.prefab then                                     -- 若未注入预制体，直接返回
        Debug.LogWarning("[Lua][Spawner] 未注入 prefab，忽略一次生成。")
        return
    end
    local go = Object.Instantiate(self.prefab)                   -- 实例化一个预制体实例
    if self._parent then                                         -- 若注入了父节点，则作为其子物体
        go.transform:SetParent(self._parent.transform, false)    -- false 表示保持本地坐标不变
    end
    local y = Random.Range(self.minHeight, self.maxHeight)       -- 在[minHeight,maxHeight)内取随机值
    go.transform.position = self.transform.position              -- 先放到生成器的世界位置
        + Vector3.up * y                                         -- 再加上随机的竖直偏移
end
-- #endregion

-- #region ========= 生命周期：Awake/OnEnable/OnDisable =========
function M:Awake()
    -- ====== 读取 Inspector 注入的数值（n:xxx 会写入同名变量） ======
    if spawnRate  ~= nil then self.spawnRate = spawnRate  end    -- 覆盖默认生成间隔
    if minHeight  ~= nil then self.minHeight = minHeight  end    -- 覆盖默认下界
    if maxHeight  ~= nil then self.maxHeight = maxHeight  end    -- 覆盖默认上界

    -- ====== 读取对象注入（可拖组件或 GameObject，统一转为 GameObject） ======
    if spawnParent ~= nil then
        self._parent = spawnParent.gameObject and                -- 若拖进来的是组件，取其 gameObject
                        spawnParent.gameObject or spawnParent    -- 否则认为本身就是 GameObject
    end

    -- ====== 参数健壮性：确保区间正确、间隔合理 ======
    if self.minHeight > self.maxHeight then                      -- 若写反了就交换
        self.minHeight, self.maxHeight = self.maxHeight, self.minHeight
    end
    if self.spawnRate <= 0 then                                  -- 非法间隔给个兜底值
        Debug.LogWarning("[Lua][Spawner] spawnRate <= 0，自动设为 0.02s")
        self.spawnRate = 0.02
    end
end

function M:OnEnable()
    -- 若已存在旧协程（比如对象池复用再次启用），先停掉
    if self._co ~= nil then self:StopCoroutine(self._co) self._co = nil end

    -- 预创建等待对象可减少 GC；WaitForSeconds 受 Time.timeScale 影响
    local wait = WaitForSeconds(self.spawnRate)

    -- 以协程的方式“每隔 spawnRate 秒生成一次”
    self._co = self:StartCoroutine(util.cs_generator(function()
        -- 设计为“先等一段时间再生成”，等价 C# 的 InvokeRepeating(spawnRate, spawnRate)
        while true do
            coroutine.yield(wait)                                -- 等待 spawnRate 秒
            spawn(self)                                          -- 执行一次生成
        end
    end))
end

function M:OnDisable()
    -- 禁用时停止协程，避免在隐藏/回收状态下仍然生成
    if self._co then
        self:StopCoroutine(self._co)
        self._co = nil
    end
end
-- #endregion

return M
