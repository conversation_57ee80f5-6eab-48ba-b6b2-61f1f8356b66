// ==============================
// 文件：VersionManifest.cs（通用）
// 作用：客户端/工具共用的 manifest 数据结构（用于差分更新/依赖）
// ==============================
using System;
using System.Collections.Generic;
using UnityEngine;

namespace CommonFramework.Utils
{
    [Serializable]
    public class BundleInfo
    {
        public string name;       // bundle 名
        public string hash;       // 文件 hash（MD5/SHA1 任一）
        public long size;         // 字节大小
        public string[] deps;     // 依赖 bundle 列表
    }

    [Serializable]
    public class VersionManifest
    {
        public int version;                   // 版本号（自增）
        public List<BundleInfo> bundles;      // 所有 bundle 信息

        public BundleInfo Find(string bundleName)
            => bundles?.Find(b => string.Equals(b.name, bundleName, StringComparison.OrdinalIgnoreCase));
    }
}
